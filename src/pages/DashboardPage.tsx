import { useNavigate } from 'react-router-dom';
import Dashboard from '@/components/Dashboard';
import { useUser } from '@/hooks/use-user';

const DashboardPage = () => {
  const navigate = useNavigate();
  const { signOut } = useUser();

  const handleLogout = async () => {
    await signOut();
    navigate('/');
  };

  return <Dashboard onLogout={handleLogout} />;
};

export default DashboardPage;
