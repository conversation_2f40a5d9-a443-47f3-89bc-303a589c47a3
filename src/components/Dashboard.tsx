import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Upload,
  Download,
  LogOut,
  User,
  Settings,
  CreditCard,
  Crown,
} from 'lucide-react';
import FileUpload from '@/components/FileUpload';
import ExtractionHistory from '@/components/ExtractionHistory';
import ExtractionResults from '@/components/ExtractionResults';
import SubscriptionManager from '@/components/SubscriptionManager';
import { useUser } from '@/hooks/use-user';

interface DashboardProps {
  onLogout: () => void;
}

const Dashboard = ({ onLogout }: DashboardProps) => {
  const [activeTab, setActiveTab] = useState('upload');
  const [extractedData, setExtractedData] = useState(null);
  const { profile, credits, stats, refreshCredits, refreshStats } = useUser();

  const handleFileProcessed = (data: unknown) => {
    setExtractedData(data);
    setActiveTab('results');
    refreshStats(); // Refresh stats after processing
    refreshCredits(); // Refresh credits after processing
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">InvoiceAI</span>
          </div>
          <div className="flex items-center space-x-4">
            {credits && (
              <div className="flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full">
                <CreditCard className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-700">
                  {credits.credits_remaining} credits
                </span>
              </div>
            )}
            <span className="text-sm text-gray-600">
              Welcome, {profile?.full_name || 'User'}
            </span>
            <Button variant="ghost" size="sm">
              <User className="h-4 w-4 mr-2" />
              Profile
            </Button>
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="ghost" size="sm" onClick={onLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Document Processing Dashboard
          </h1>
          <p className="text-gray-600">
            Upload invoices and receipts to extract structured data
            automatically
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Credits Remaining
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {credits?.credits_remaining || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Available for processing
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Documents Processed
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.documentsProcessed}
              </div>
              <p className="text-xs text-muted-foreground">
                Total documents uploaded
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Data Points Extracted
              </CardTitle>
              <Upload className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.dataPointsExtracted}
              </div>
              <p className="text-xs text-muted-foreground">
                Fields extracted from documents
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Exports Generated
              </CardTitle>
              <Download className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.exportsGenerated}</div>
              <p className="text-xs text-muted-foreground">
                CSV exports created
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Tabs */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-4 max-w-lg">
            <TabsTrigger value="upload">Upload</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="billing">Billing</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Upload Documents</CardTitle>
                <CardDescription>
                  Upload invoices, receipts, or other documents for AI-powered
                  data extraction. Each document costs 1 credit to process.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FileUpload onFileProcessed={handleFileProcessed} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="results" className="space-y-6">
            <ExtractionResults data={extractedData} />
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <ExtractionHistory />
          </TabsContent>

          <TabsContent value="billing" className="space-y-6">
            <SubscriptionManager
              userCredits={credits}
              onCreditsUpdate={refreshCredits}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
