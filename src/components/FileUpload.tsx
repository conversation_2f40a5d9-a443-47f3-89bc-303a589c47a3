import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Upload, FileText, AlertCircle, CreditCard } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useUser } from '@/hooks/use-user.ts';

interface FileUploadProps {
  onFileProcessed: (data: any) => void;
}

const FileUpload = ({ onFileProcessed }: FileUploadProps) => {
  const [uploading, setUploading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const { credits } = useUser();
  const { toast } = useToast();

  const deductCredit = async (documentId: string) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error('No user found');

      // Update credits (deduct 1)
      const { error: creditsError } = await supabase
        .from('credits')
        .update({
          credits_remaining: Math.max(0, (credits?.credits_remaining || 0) - 1),
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id);

      if (creditsError) throw creditsError;

      // Record transaction
      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: user.id,
          document_id: documentId,
          credits_used: 1,
          transaction_type: 'usage',
          description: 'Document processing',
        });

      if (transactionError) throw transactionError;
    } catch (error) {
      console.error('Error deducting credit:', error);
      throw error;
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      // Check if user has credits
      if (!credits || credits.credits_remaining <= 0) {
        toast({
          title: 'Insufficient Credits',
          description:
            'You need at least 1 credit to process a document. Please purchase more credits.',
          variant: 'destructive',
        });
        return;
      }

      const file = acceptedFiles[0];
      if (!file) return;

      // Validate file type
      const allowedTypes = [
        'application/pdf',
        'image/png',
        'image/jpeg',
        'image/jpg',
      ];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: 'Invalid File Type',
          description: 'Please upload a PDF, PNG, or JPEG file.',
          variant: 'destructive',
        });
        return;
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: 'File Too Large',
          description: 'Please upload a file smaller than 10MB.',
          variant: 'destructive',
        });
        return;
      }

      setUploading(true);
      setProgress(10);

      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          throw new Error('No authenticated user found');
        }

        // Create document record
        const { data: documentData, error: documentError } = await supabase
          .from('documents')
          .insert({
            filename: file.name,
            file_size: file.size,
            file_type: file.type,
            user_id: user.id,
            status: 'uploaded',
          })
          .select()
          .single();

        if (documentError) throw documentError;

        setProgress(30);

        // Upload file to storage
        const fileExt = file.name.split('.').pop();
        const fileName = `${documentData.id}.${fileExt}`;

        // Update document record with upload_url
        const { error: updateError } = await supabase
          .from('documents')
          .update({ upload_url: fileName })
          .eq('id', documentData.id);

        if (updateError) throw updateError;

        const { error: uploadError } = await supabase.storage
          .from('documents')
          .upload(fileName, file);

        if (uploadError) throw uploadError;

        setProgress(50);
        setUploading(false);
        setProcessing(true);

        // Deduct credit before processing
        await deductCredit(documentData.id);

        // Process document with AI
        const { data: functionData, error: functionError } =
          await supabase.functions.invoke('process-document', {
            body: {
              documentId: documentData.id,
              fileName: file.name,
              fileUrl: fileName,
            },
          });

        if (functionError) throw functionError;

        setProgress(100);

        toast({
          title: 'Document Processed',
          description: 'Your document has been successfully processed!',
        });

        onFileProcessed(functionData.extraction);
      } catch (error) {
        console.error('Error processing file:', error);
        toast({
          title: 'Processing Failed',
          description:
            error.message || 'Failed to process document. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setUploading(false);
        setProcessing(false);
        setProgress(0);
      }
    },
    [credits, toast, onFileProcessed]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
    },
    multiple: false,
    disabled: uploading || processing,
  });

  const isProcessing = uploading || processing;

  return (
    <div className="space-y-4">
      {/* Credits Display */}
      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border">
        <div className="flex items-center gap-2">
          <CreditCard className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-700">
            Available Credits: {credits?.credits_remaining || 0}
          </span>
        </div>
        <span className="text-xs text-blue-600">1 credit per document</span>
      </div>

      {/* Upload Area */}
      <Card
        className={`border-2 border-dashed transition-colors ${
          isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
        } ${isProcessing ? 'opacity-50 pointer-events-none' : ''}`}
      >
        <CardContent className="p-8">
          <div {...getRootProps()} className="cursor-pointer text-center">
            <input {...getInputProps()} />

            {isProcessing ? (
              <div className="space-y-4">
                <div className="animate-spin mx-auto h-12 w-12 text-blue-600">
                  <Upload className="h-full w-full" />
                </div>
                <div className="space-y-2">
                  <p className="text-lg font-medium">
                    {uploading ? 'Uploading...' : 'Processing with AI...'}
                  </p>
                  <Progress
                    value={progress}
                    className="w-full max-w-md mx-auto"
                  />
                  <p className="text-sm text-gray-600">
                    {uploading
                      ? 'Uploading your document...'
                      : 'Extracting data using AI...'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">
                    Drop your document here, or click to browse
                  </p>
                  <p className="text-sm text-gray-600">
                    Supports PDF, PNG, and JPEG files up to 10MB
                  </p>
                </div>

                {(!credits || credits.credits_remaining <= 0) && (
                  <div className="flex items-center justify-center gap-2 text-orange-600 bg-orange-50 p-3 rounded-lg">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      No credits available. Please purchase credits to process
                      documents.
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Supported Formats */}
      <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
        <div className="flex items-center space-x-1">
          <FileText className="h-4 w-4" />
          <span>PDF</span>
        </div>
        <div className="flex items-center space-x-1">
          <FileText className="h-4 w-4" />
          <span>PNG</span>
        </div>
        <div className="flex items-center space-x-1">
          <FileText className="h-4 w-4" />
          <span>JPEG</span>
        </div>
      </div>
    </div>
  );
};

export default FileUpload;
